//@version=5
indicator('Pivot Points Screener for Top 20 Bybit Futures', overlay=true)

////////////
// INPUTS //

timeframe_input = input.string("Weekly", "Pivot Timeframe", options=["Weekly", "Monthly", "Yearly"], group="Settings")
col_width = input.float(12, title="Column Width (%)", group="Settings")
scr_numb = input.int(1, title="Screen #", tooltip='1 - rightmost screener', minval=1, group="Settings")

/////////////
// SYMBOLS //

u01 = input.bool(true,  title="", group='Symbols', inline='s01')
u02 = input.bool(true,  title="", group='Symbols', inline='s02')
u03 = input.bool(true,  title="", group='Symbols', inline='s03')
u04 = input.bool(true,  title="", group='Symbols', inline='s04')
u05 = input.bool(true,  title="", group='Symbols', inline='s05')
u06 = input.bool(true,  title="", group='Symbols', inline='s06')
u07 = input.bool(true,  title="", group='Symbols', inline='s07')
u08 = input.bool(true,  title="", group='Symbols', inline='s08')
u09 = input.bool(true,  title="", group='Symbols', inline='s09')
u10 = input.bool(true,  title="", group='Symbols', inline='s10')
u11 = input.bool(true,  title="", group='Symbols', inline='s11')
u12 = input.bool(true,  title="", group='Symbols', inline='s12')
u13 = input.bool(true,  title="", group='Symbols', inline='s13')
u14 = input.bool(true,  title="", group='Symbols', inline='s14')
u15 = input.bool(true,  title="", group='Symbols', inline='s15')
u16 = input.bool(true,  title="", group='Symbols', inline='s16')
u17 = input.bool(true,  title="", group='Symbols', inline='s17')
u18 = input.bool(true,  title="", group='Symbols', inline='s18')
u19 = input.bool(true,  title="", group='Symbols', inline='s19')
u20 = input.bool(true,  title="", group='Symbols', inline='s20')


s01 = input.symbol('BYBIT:BTCUSDT.P',        group='Symbols', inline='s01')
s02 = input.symbol('BYBIT:ETHUSDT.P',        group='Symbols', inline='s02')
s03 = input.symbol('BYBIT:SOLUSDT.P',        group='Symbols', inline='s03')
s04 = input.symbol('BYBIT:XRPUSDT.P',        group='Symbols', inline='s04')
s05 = input.symbol('BYBIT:ASTERUSDT.P',      group='Symbols', inline='s05')
s06 = input.symbol('BYBIT:DOGEUSDT.P',       group='Symbols', inline='s06')
s07 = input.symbol('BYBIT:HYPEUSDT.P',       group='Symbols', inline='s07')
s08 = input.symbol('BYBIT:SUIUSDT.P',        group='Symbols', inline='s08')
s09 = input.symbol('BYBIT:STBLUSDT.P',       group='Symbols', inline='s09')
s10 = input.symbol('BYBIT:AVAXUSDT.P',       group='Symbols', inline='s10')
s11 = input.symbol('BYBIT:AVNTUSDT.P',       group='Symbols', inline='s11')
s12 = input.symbol('BYBIT:ADAUSDT.P',        group='Symbols', inline='s12')
s13 = input.symbol('BYBIT:FARTCOINUSDT.P',   group='Symbols', inline='s13')
s14 = input.symbol('BYBIT:ENAUSDT.P',        group='Symbols', inline='s14')
s15 = input.symbol('BYBIT:PUMPFUNUSDT.P',    group='Symbols', inline='s15')
s16 = input.symbol('BYBIT:WIFUSDT.P',        group='Symbols', inline='s16')
s17 = input.symbol('BYBIT:LINKUSDT.P',       group='Symbols', inline='s17')
s18 = input.symbol('BYBIT:DOTUSDT.P',        group='Symbols', inline='s18')
s19 = input.symbol('BYBIT:FWOGUSDT.P',      group='Symbols', inline='s19')
s20 = input.symbol('BYBIT:LTCUSDT.P',        group='Symbols', inline='s20')


//////////////////
// CALCULATIONS //

// Get only symbol
only_symbol(s) =>
    array.get(str.split(s, ":"), 1)

// Get pivot timeframe
get_pivot_timeframe() =>
    switch timeframe_input
        "Weekly" => "1W"
        "Monthly" => "1M"
        "Yearly" => "1Y"
        => "1W"

// Calculate traditional pivot points
// Example calculation for verification:
// If High = 100, Low = 90, Close = 95
// PP = (100 + 90 + 95) / 3 = 95
// R1 = (2 * 95) - 90 = 100
// S1 = (2 * 95) - 100 = 90
// R2 = 95 + (100 - 90) = 105
// S2 = 95 - (100 - 90) = 85
// R3 = 100 + 2 * (95 - 90) = 110
// S3 = 90 - 2 * (100 - 95) = 80
calc_traditional_pivots(ph, pl, pc) =>
    pp = (ph + pl + pc) / 3
    r1 = pp * 2 - pl
    s1 = pp * 2 - ph
    r2 = pp + (ph - pl)
    s2 = pp - (ph - pl)
    r3 = ph + 2 * (pp - pl)
    s3 = pl - 2 * (ph - pp)
    // Additional levels using range extension method
    price_range = ph - pl
    r4 = r3 + price_range
    s4 = s3 - price_range
    r5 = r4 + price_range
    s5 = s4 - price_range
    [pp, r1, r2, r3, r4, r5, s1, s2, s3, s4, s5]

// Find closest pivot level
find_closest_pivot(current_price, pp, r1, r2, r3, r4, r5, s1, s2, s3, s4, s5) =>
    levels = array.from(s5, s4, s3, s2, s1, pp, r1, r2, r3, r4, r5)
    names = array.from("S5", "S4", "S3", "S2", "S1", "PP", "R1", "R2", "R3", "R4", "R5")

    min_distance = math.abs(current_price - array.get(levels, 0))
    closest_index = 0

    for i = 1 to array.size(levels) - 1
        distance = math.abs(current_price - array.get(levels, i))
        if distance < min_distance
            min_distance := distance
            closest_index := i

    array.get(names, closest_index)

// Function to get pivot data for a specific symbol
get_symbol_pivot_data(symbol_id) =>
    pivot_tf = get_pivot_timeframe()

    // Get OHLC data from pivot timeframe - removed lookahead for accuracy
    [ph, pl, pc] = request.security(symbol_id, pivot_tf, [high[1], low[1], close[1]], lookahead = barmerge.lookahead_off)

    // Get current price for the symbol
    current_price = request.security(symbol_id, timeframe.period, close, lookahead = barmerge.lookahead_off)

    // Calculate pivot points
    [pp, r1, r2, r3, r4, r5, s1, s2, s3, s4, s5] = calc_traditional_pivots(ph, pl, pc)

    // Find closest pivot to current price
    closest_pivot = find_closest_pivot(current_price, pp, r1, r2, r3, r4, r5, s1, s2, s3, s4, s5)

    closest_pivot

// Get pivot data for all symbols - removed nested security calls
pivot01 = get_symbol_pivot_data(s01)
pivot02 = get_symbol_pivot_data(s02)
pivot03 = get_symbol_pivot_data(s03)
pivot04 = get_symbol_pivot_data(s04)
pivot05 = get_symbol_pivot_data(s05)
pivot06 = get_symbol_pivot_data(s06)
pivot07 = get_symbol_pivot_data(s07)
pivot08 = get_symbol_pivot_data(s08)
pivot09 = get_symbol_pivot_data(s09)
pivot10 = get_symbol_pivot_data(s10)
pivot11 = get_symbol_pivot_data(s11)
pivot12 = get_symbol_pivot_data(s12)
pivot13 = get_symbol_pivot_data(s13)
pivot14 = get_symbol_pivot_data(s14)
pivot15 = get_symbol_pivot_data(s15)
pivot16 = get_symbol_pivot_data(s16)
pivot17 = get_symbol_pivot_data(s17)
pivot18 = get_symbol_pivot_data(s18)
pivot19 = get_symbol_pivot_data(s19)
pivot20 = get_symbol_pivot_data(s20)


////////////
// ARRAYS //

s_arr = array.new_string(0)
u_arr = array.new_bool(0)
pivot_arr = array.new_string(0)

// Add Symbols
array.push(s_arr, only_symbol(s01))
array.push(s_arr, only_symbol(s02))
array.push(s_arr, only_symbol(s03))
array.push(s_arr, only_symbol(s04))
array.push(s_arr, only_symbol(s05))
array.push(s_arr, only_symbol(s06))
array.push(s_arr, only_symbol(s07))
array.push(s_arr, only_symbol(s08))
array.push(s_arr, only_symbol(s09))
array.push(s_arr, only_symbol(s10))
array.push(s_arr, only_symbol(s11))
array.push(s_arr, only_symbol(s12))
array.push(s_arr, only_symbol(s13))
array.push(s_arr, only_symbol(s14))
array.push(s_arr, only_symbol(s15))
array.push(s_arr, only_symbol(s16))
array.push(s_arr, only_symbol(s17))
array.push(s_arr, only_symbol(s18))
array.push(s_arr, only_symbol(s19))
array.push(s_arr, only_symbol(s20))


// FLAGS
array.push(u_arr, u01)
array.push(u_arr, u02)
array.push(u_arr, u03)
array.push(u_arr, u04)
array.push(u_arr, u05)
array.push(u_arr, u06)
array.push(u_arr, u07)
array.push(u_arr, u08)
array.push(u_arr, u09)
array.push(u_arr, u10)
array.push(u_arr, u11)
array.push(u_arr, u12)
array.push(u_arr, u13)
array.push(u_arr, u14)
array.push(u_arr, u15)
array.push(u_arr, u16)
array.push(u_arr, u17)
array.push(u_arr, u18)
array.push(u_arr, u19)
array.push(u_arr, u20)


// PIVOT LEVELS
array.push(pivot_arr, pivot01)
array.push(pivot_arr, pivot02)
array.push(pivot_arr, pivot03)
array.push(pivot_arr, pivot04)
array.push(pivot_arr, pivot05)
array.push(pivot_arr, pivot06)
array.push(pivot_arr, pivot07)
array.push(pivot_arr, pivot08)
array.push(pivot_arr, pivot09)
array.push(pivot_arr, pivot10)
array.push(pivot_arr, pivot11)
array.push(pivot_arr, pivot12)
array.push(pivot_arr, pivot13)
array.push(pivot_arr, pivot14)
array.push(pivot_arr, pivot15)
array.push(pivot_arr, pivot16)
array.push(pivot_arr, pivot17)
array.push(pivot_arr, pivot18)
array.push(pivot_arr, pivot19)
array.push(pivot_arr, pivot20)


///////////
// PLOTS //

// ---- Styling (softer palette and larger fonts) ----
header_bg = color.rgb(40, 44, 52)
header_text = color.white
label_bg = color.rgb(64, 68, 75)
label_text = color.white
pivot_text = color.black

// Pivot palette (muted)
col_r_strong = color.rgb(214, 93, 93)
col_r_soft   = color.rgb(224, 149, 64)
col_pp       = color.rgb(100, 149, 237)
col_s_soft   = color.rgb(129, 199, 132)
col_s_deep   = color.rgb(102, 187, 106)
col_neutral  = color.rgb(120, 144, 156)

var tbl = table.new(position.top_right, 3, 21, frame_color=header_bg, frame_width=1, border_width=2, border_color=color.new(header_bg, 0))

if barstate.islast
    table.cell(tbl, 0, 0, 'Symbol', width=col_width, text_halign=text.align_center, bgcolor=header_bg, text_color=header_text, text_size=size.large)
    table.cell(tbl, 1, 0, timeframe_input + ' Pivot', width=col_width, text_halign=text.align_center, bgcolor=header_bg, text_color=header_text, text_size=size.large)
    if (scr_numb > 1)
        table.cell(tbl, 2, 0, '', width=col_width * 2 * (scr_numb - 1), text_halign=text.align_center, bgcolor=color.new(header_bg, 30), text_size=size.normal)

    for i = 0 to 19
        if array.get(u_arr, i)
            pivot_level = array.get(pivot_arr, i)

            // Color coding for pivot levels (muted palette)
            pivot_color = pivot_level == "R5" or pivot_level == "R4" or pivot_level == "R3" ? col_r_strong :
                         pivot_level == "R2" or pivot_level == "R1" ? col_r_soft :
                         pivot_level == "PP" ? col_pp :
                         pivot_level == "S1" or pivot_level == "S2" ? col_s_soft :
                         pivot_level == "S3" or pivot_level == "S4" or pivot_level == "S5" ? col_s_deep :
                         col_neutral

            table.cell(tbl, 0, i + 1, array.get(s_arr, i), text_halign=text.align_center, bgcolor=label_bg, text_color=label_text, text_size=size.normal)
            table.cell(tbl, 1, i + 1, pivot_level, text_halign=text.align_center, bgcolor=pivot_color, text_color=pivot_text, text_size=size.normal)