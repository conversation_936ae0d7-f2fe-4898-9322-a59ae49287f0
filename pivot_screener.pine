//@version=5
indicator('Pivot Points Screener for Top 20 Bybit Futures', overlay=true)

////////////
// INPUTS //

col_width = input.float(10, title="Column Width (%)", group="Settings")
scr_numb = input.int(1, title="Screen #", tooltip='1 - rightmost screener', minval=1, group="Settings")

/////////////
// SYMBOLS //

u01 = input.bool(true,  title="", group='Symbols', inline='s01')
u02 = input.bool(true,  title="", group='Symbols', inline='s02')
u03 = input.bool(true,  title="", group='Symbols', inline='s03')
u04 = input.bool(true,  title="", group='Symbols', inline='s04')
u05 = input.bool(true,  title="", group='Symbols', inline='s05')
u06 = input.bool(true,  title="", group='Symbols', inline='s06')
u07 = input.bool(true,  title="", group='Symbols', inline='s07')
u08 = input.bool(true,  title="", group='Symbols', inline='s08')
u09 = input.bool(true,  title="", group='Symbols', inline='s09')
u10 = input.bool(true,  title="", group='Symbols', inline='s10')
u11 = input.bool(true,  title="", group='Symbols', inline='s11')
u12 = input.bool(true,  title="", group='Symbols', inline='s12')
u13 = input.bool(true,  title="", group='Symbols', inline='s13')
u14 = input.bool(true,  title="", group='Symbols', inline='s14')
u15 = input.bool(true,  title="", group='Symbols', inline='s15')
u16 = input.bool(true,  title="", group='Symbols', inline='s16')
u17 = input.bool(true,  title="", group='Symbols', inline='s17')
u18 = input.bool(true,  title="", group='Symbols', inline='s18')
u19 = input.bool(true,  title="", group='Symbols', inline='s19')
u20 = input.bool(true,  title="", group='Symbols', inline='s20')


s01 = input.symbol('BYBIT:BTCUSDT.P',        group='Symbols', inline='s01')
s02 = input.symbol('BYBIT:ETHUSDT.P',        group='Symbols', inline='s02')
s03 = input.symbol('BYBIT:SOLUSDT.P',        group='Symbols', inline='s03')
s04 = input.symbol('BYBIT:XRPUSDT.P',        group='Symbols', inline='s04')
s05 = input.symbol('BYBIT:ASTERUSDT.P',      group='Symbols', inline='s05')
s06 = input.symbol('BYBIT:DOGEUSDT.P',       group='Symbols', inline='s06')
s07 = input.symbol('BYBIT:HYPEUSDT.P',       group='Symbols', inline='s07')
s08 = input.symbol('BYBIT:SUIUSDT.P',        group='Symbols', inline='s08')
s09 = input.symbol('BYBIT:STBLUSDT.P',       group='Symbols', inline='s09')
s10 = input.symbol('BYBIT:AVAXUSDT.P',       group='Symbols', inline='s10')
s11 = input.symbol('BYBIT:AVNTUSDT.P',       group='Symbols', inline='s11')
s12 = input.symbol('BYBIT:ADAUSDT.P',        group='Symbols', inline='s12')
s13 = input.symbol('BYBIT:FARTCOINUSDT.P',   group='Symbols', inline='s13')
s14 = input.symbol('BYBIT:ENAUSDT.P',        group='Symbols', inline='s14')
s15 = input.symbol('BYBIT:PUMPFUNUSDT.P',    group='Symbols', inline='s15')
s16 = input.symbol('BYBIT:WIFUSDT.P',        group='Symbols', inline='s16')
s17 = input.symbol('BYBIT:LINKUSDT.P',       group='Symbols', inline='s17')
s18 = input.symbol('BYBIT:DOTUSDT.P',        group='Symbols', inline='s18')
s19 = input.symbol('BYBIT:FWOGUSDT.P',      group='Symbols', inline='s19')
s20 = input.symbol('BYBIT:LTCUSDT.P',        group='Symbols', inline='s20')


//////////////////
// CALCULATIONS //

// Get only symbol
only_symbol(s) =>
    array.get(str.split(s, ":"), 1)

// Get all pivot timeframes
get_all_pivot_timeframes() =>
    ["1W", "1M", "1Y"]

// Calculate traditional pivot points
// Example calculation for verification:
// If High = 100, Low = 90, Close = 95
// PP = (100 + 90 + 95) / 3 = 95
// R1 = (2 * 95) - 90 = 100
// S1 = (2 * 95) - 100 = 90
// R2 = 95 + (100 - 90) = 105
// S2 = 95 - (100 - 90) = 85
// R3 = 100 + 2 * (95 - 90) = 110
// S3 = 90 - 2 * (100 - 95) = 80
calc_traditional_pivots(ph, pl, pc) =>
    pp = (ph + pl + pc) / 3
    r1 = pp * 2 - pl
    s1 = pp * 2 - ph
    r2 = pp + (ph - pl)
    s2 = pp - (ph - pl)
    r3 = ph + 2 * (pp - pl)
    s3 = pl - 2 * (ph - pp)
    // Additional levels using range extension method
    price_range = ph - pl
    r4 = r3 + price_range
    s4 = s3 - price_range
    r5 = r4 + price_range
    s5 = s4 - price_range
    [pp, r1, r2, r3, r4, r5, s1, s2, s3, s4, s5]

// Find closest pivot level
find_closest_pivot(current_price, pp, r1, r2, r3, r4, r5, s1, s2, s3, s4, s5) =>
    levels = array.from(s5, s4, s3, s2, s1, pp, r1, r2, r3, r4, r5)
    names = array.from("S5", "S4", "S3", "S2", "S1", "PP", "R1", "R2", "R3", "R4", "R5")

    min_distance = math.abs(current_price - array.get(levels, 0))
    closest_index = 0

    for i = 1 to array.size(levels) - 1
        distance = math.abs(current_price - array.get(levels, i))
        if distance < min_distance
            min_distance := distance
            closest_index := i

    array.get(names, closest_index)

// Function to get pivot data for all timeframes for a specific symbol
get_symbol_all_pivot_data(symbol_id) =>
    // Get current price for the symbol (only one call needed)
    current_price = request.security(symbol_id, timeframe.period, close, lookahead = barmerge.lookahead_off)

    // Get OHLC data for weekly timeframe
    [ph_w, pl_w, pc_w] = request.security(symbol_id, "1W", [high[1], low[1], close[1]], lookahead = barmerge.lookahead_off)

    // Get OHLC data for monthly timeframe
    [ph_m, pl_m, pc_m] = request.security(symbol_id, "1M", [high[1], low[1], close[1]], lookahead = barmerge.lookahead_off)

    // Get OHLC data for yearly timeframe
    [ph_y, pl_y, pc_y] = request.security(symbol_id, "1Y", [high[1], low[1], close[1]], lookahead = barmerge.lookahead_off)

    // Calculate pivot points for weekly
    [pp_w, r1_w, r2_w, r3_w, r4_w, r5_w, s1_w, s2_w, s3_w, s4_w, s5_w] = calc_traditional_pivots(ph_w, pl_w, pc_w)
    closest_pivot_w = find_closest_pivot(current_price, pp_w, r1_w, r2_w, r3_w, r4_w, r5_w, s1_w, s2_w, s3_w, s4_w, s5_w)

    // Calculate pivot points for monthly
    [pp_m, r1_m, r2_m, r3_m, r4_m, r5_m, s1_m, s2_m, s3_m, s4_m, s5_m] = calc_traditional_pivots(ph_m, pl_m, pc_m)
    closest_pivot_m = find_closest_pivot(current_price, pp_m, r1_m, r2_m, r3_m, r4_m, r5_m, s1_m, s2_m, s3_m, s4_m, s5_m)

    // Calculate pivot points for yearly
    [pp_y, r1_y, r2_y, r3_y, r4_y, r5_y, s1_y, s2_y, s3_y, s4_y, s5_y] = calc_traditional_pivots(ph_y, pl_y, pc_y)
    closest_pivot_y = find_closest_pivot(current_price, pp_y, r1_y, r2_y, r3_y, r4_y, r5_y, s1_y, s2_y, s3_y, s4_y, s5_y)

    [closest_pivot_w, closest_pivot_m, closest_pivot_y]

// Get pivot data for all symbols and all timeframes
[pivot01_w, pivot01_m, pivot01_y] = get_symbol_all_pivot_data(s01)
[pivot02_w, pivot02_m, pivot02_y] = get_symbol_all_pivot_data(s02)
[pivot03_w, pivot03_m, pivot03_y] = get_symbol_all_pivot_data(s03)
[pivot04_w, pivot04_m, pivot04_y] = get_symbol_all_pivot_data(s04)
[pivot05_w, pivot05_m, pivot05_y] = get_symbol_all_pivot_data(s05)
[pivot06_w, pivot06_m, pivot06_y] = get_symbol_all_pivot_data(s06)
[pivot07_w, pivot07_m, pivot07_y] = get_symbol_all_pivot_data(s07)
[pivot08_w, pivot08_m, pivot08_y] = get_symbol_all_pivot_data(s08)
[pivot09_w, pivot09_m, pivot09_y] = get_symbol_all_pivot_data(s09)
[pivot10_w, pivot10_m, pivot10_y] = get_symbol_all_pivot_data(s10)
[pivot11_w, pivot11_m, pivot11_y] = get_symbol_all_pivot_data(s11)
[pivot12_w, pivot12_m, pivot12_y] = get_symbol_all_pivot_data(s12)
[pivot13_w, pivot13_m, pivot13_y] = get_symbol_all_pivot_data(s13)
[pivot14_w, pivot14_m, pivot14_y] = get_symbol_all_pivot_data(s14)
[pivot15_w, pivot15_m, pivot15_y] = get_symbol_all_pivot_data(s15)
[pivot16_w, pivot16_m, pivot16_y] = get_symbol_all_pivot_data(s16)
[pivot17_w, pivot17_m, pivot17_y] = get_symbol_all_pivot_data(s17)
[pivot18_w, pivot18_m, pivot18_y] = get_symbol_all_pivot_data(s18)
[pivot19_w, pivot19_m, pivot19_y] = get_symbol_all_pivot_data(s19)
[pivot20_w, pivot20_m, pivot20_y] = get_symbol_all_pivot_data(s20)


////////////
// ARRAYS //

s_arr = array.new_string(0)
u_arr = array.new_bool(0)
pivot_w_arr = array.new_string(0)
pivot_m_arr = array.new_string(0)
pivot_y_arr = array.new_string(0)

// Add Symbols
array.push(s_arr, only_symbol(s01))
array.push(s_arr, only_symbol(s02))
array.push(s_arr, only_symbol(s03))
array.push(s_arr, only_symbol(s04))
array.push(s_arr, only_symbol(s05))
array.push(s_arr, only_symbol(s06))
array.push(s_arr, only_symbol(s07))
array.push(s_arr, only_symbol(s08))
array.push(s_arr, only_symbol(s09))
array.push(s_arr, only_symbol(s10))
array.push(s_arr, only_symbol(s11))
array.push(s_arr, only_symbol(s12))
array.push(s_arr, only_symbol(s13))
array.push(s_arr, only_symbol(s14))
array.push(s_arr, only_symbol(s15))
array.push(s_arr, only_symbol(s16))
array.push(s_arr, only_symbol(s17))
array.push(s_arr, only_symbol(s18))
array.push(s_arr, only_symbol(s19))
array.push(s_arr, only_symbol(s20))


// FLAGS
array.push(u_arr, u01)
array.push(u_arr, u02)
array.push(u_arr, u03)
array.push(u_arr, u04)
array.push(u_arr, u05)
array.push(u_arr, u06)
array.push(u_arr, u07)
array.push(u_arr, u08)
array.push(u_arr, u09)
array.push(u_arr, u10)
array.push(u_arr, u11)
array.push(u_arr, u12)
array.push(u_arr, u13)
array.push(u_arr, u14)
array.push(u_arr, u15)
array.push(u_arr, u16)
array.push(u_arr, u17)
array.push(u_arr, u18)
array.push(u_arr, u19)
array.push(u_arr, u20)


// PIVOT LEVELS - WEEKLY
array.push(pivot_w_arr, pivot01_w)
array.push(pivot_w_arr, pivot02_w)
array.push(pivot_w_arr, pivot03_w)
array.push(pivot_w_arr, pivot04_w)
array.push(pivot_w_arr, pivot05_w)
array.push(pivot_w_arr, pivot06_w)
array.push(pivot_w_arr, pivot07_w)
array.push(pivot_w_arr, pivot08_w)
array.push(pivot_w_arr, pivot09_w)
array.push(pivot_w_arr, pivot10_w)
array.push(pivot_w_arr, pivot11_w)
array.push(pivot_w_arr, pivot12_w)
array.push(pivot_w_arr, pivot13_w)
array.push(pivot_w_arr, pivot14_w)
array.push(pivot_w_arr, pivot15_w)
array.push(pivot_w_arr, pivot16_w)
array.push(pivot_w_arr, pivot17_w)
array.push(pivot_w_arr, pivot18_w)
array.push(pivot_w_arr, pivot19_w)
array.push(pivot_w_arr, pivot20_w)

// PIVOT LEVELS - MONTHLY
array.push(pivot_m_arr, pivot01_m)
array.push(pivot_m_arr, pivot02_m)
array.push(pivot_m_arr, pivot03_m)
array.push(pivot_m_arr, pivot04_m)
array.push(pivot_m_arr, pivot05_m)
array.push(pivot_m_arr, pivot06_m)
array.push(pivot_m_arr, pivot07_m)
array.push(pivot_m_arr, pivot08_m)
array.push(pivot_m_arr, pivot09_m)
array.push(pivot_m_arr, pivot10_m)
array.push(pivot_m_arr, pivot11_m)
array.push(pivot_m_arr, pivot12_m)
array.push(pivot_m_arr, pivot13_m)
array.push(pivot_m_arr, pivot14_m)
array.push(pivot_m_arr, pivot15_m)
array.push(pivot_m_arr, pivot16_m)
array.push(pivot_m_arr, pivot17_m)
array.push(pivot_m_arr, pivot18_m)
array.push(pivot_m_arr, pivot19_m)
array.push(pivot_m_arr, pivot20_m)

// PIVOT LEVELS - YEARLY
array.push(pivot_y_arr, pivot01_y)
array.push(pivot_y_arr, pivot02_y)
array.push(pivot_y_arr, pivot03_y)
array.push(pivot_y_arr, pivot04_y)
array.push(pivot_y_arr, pivot05_y)
array.push(pivot_y_arr, pivot06_y)
array.push(pivot_y_arr, pivot07_y)
array.push(pivot_y_arr, pivot08_y)
array.push(pivot_y_arr, pivot09_y)
array.push(pivot_y_arr, pivot10_y)
array.push(pivot_y_arr, pivot11_y)
array.push(pivot_y_arr, pivot12_y)
array.push(pivot_y_arr, pivot13_y)
array.push(pivot_y_arr, pivot14_y)
array.push(pivot_y_arr, pivot15_y)
array.push(pivot_y_arr, pivot16_y)
array.push(pivot_y_arr, pivot17_y)
array.push(pivot_y_arr, pivot18_y)
array.push(pivot_y_arr, pivot19_y)
array.push(pivot_y_arr, pivot20_y)


///////////
// PLOTS //

// ---- Styling (softer palette and larger fonts) ----
header_bg = color.rgb(40, 44, 52)
header_text = color.white
label_bg = color.rgb(64, 68, 75)
label_text = color.white
pivot_text = color.black

// Pivot palette (muted)
col_r_strong = color.rgb(214, 93, 93)
col_r_soft   = color.rgb(224, 149, 64)
col_pp       = color.rgb(100, 149, 237)
col_s_soft   = color.rgb(129, 199, 132)
col_s_deep   = color.rgb(102, 187, 106)
col_neutral  = color.rgb(120, 144, 156)

var tbl = table.new(position.top_right, 5, 21, frame_color=header_bg, frame_width=1, border_width=2, border_color=color.new(header_bg, 0))

if barstate.islast
    table.cell(tbl, 0, 0, 'Symbol', width=col_width, text_halign=text.align_center, bgcolor=header_bg, text_color=header_text, text_size=size.large)
    table.cell(tbl, 1, 0, 'Weekly', width=col_width, text_halign=text.align_center, bgcolor=header_bg, text_color=header_text, text_size=size.large)
    table.cell(tbl, 2, 0, 'Monthly', width=col_width, text_halign=text.align_center, bgcolor=header_bg, text_color=header_text, text_size=size.large)
    table.cell(tbl, 3, 0, 'Yearly', width=col_width, text_halign=text.align_center, bgcolor=header_bg, text_color=header_text, text_size=size.large)
    if (scr_numb > 1)
        table.cell(tbl, 4, 0, '', width=col_width * 2 * (scr_numb - 1), text_halign=text.align_center, bgcolor=color.new(header_bg, 30), text_size=size.normal)

    for i = 0 to 19
        if array.get(u_arr, i)
            pivot_level_w = array.get(pivot_w_arr, i)
            pivot_level_m = array.get(pivot_m_arr, i)
            pivot_level_y = array.get(pivot_y_arr, i)

            // Color coding function for pivot levels (muted palette)
            get_pivot_color(pivot_level) =>
                pivot_level == "R5" or pivot_level == "R4" or pivot_level == "R3" ? col_r_strong :
                pivot_level == "R2" or pivot_level == "R1" ? col_r_soft :
                pivot_level == "PP" ? col_pp :
                pivot_level == "S1" or pivot_level == "S2" ? col_s_soft :
                pivot_level == "S3" or pivot_level == "S4" or pivot_level == "S5" ? col_s_deep :
                col_neutral

            table.cell(tbl, 0, i + 1, array.get(s_arr, i), text_halign=text.align_center, bgcolor=label_bg, text_color=label_text, text_size=size.normal)
            table.cell(tbl, 1, i + 1, pivot_level_w, text_halign=text.align_center, bgcolor=get_pivot_color(pivot_level_w), text_color=pivot_text, text_size=size.normal)
            table.cell(tbl, 2, i + 1, pivot_level_m, text_halign=text.align_center, bgcolor=get_pivot_color(pivot_level_m), text_color=pivot_text, text_size=size.normal)
            table.cell(tbl, 3, i + 1, pivot_level_y, text_halign=text.align_center, bgcolor=get_pivot_color(pivot_level_y), text_color=pivot_text, text_size=size.normal)